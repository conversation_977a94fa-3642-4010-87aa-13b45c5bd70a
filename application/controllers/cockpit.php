<?php if (! defined('BASEPATH')) exit('No direct script access allowed');

// Carregar biblioteca PhpSpreadsheet
require_once 'vendor/autoload.php';

use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Common\Entity\Row;
use Box\Spout\Common\Type;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Common\Entity\Style\CellAlignment;
use Box\Spout\Common\Entity\Style\Color;

class Cockpit extends MY_Controller
{
    public $homologacoes;

    public $statusExportacao = array("0", "1");
    public $statusImplementacao = array("I", "N", "R");
    public $statusConsult;
    public $statusItens;
    public $sla;
    public $dianaUtilizacao;
    public $dianaAcuracidade;
    public $atribuicao;
    public $ncmDivergentes;
    public $perguntasRespostas;

    public function __construct()
    {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model(array(
            'empresa_model',
            'cad_item_model',
            'item_model',
            'cockpit_model',
            'cad_item_wf_atributo_model'
        ));

        $this->cad_item_model->set_namespace('cockpit');
        $this->cockpit_model->set_namespace('cockpit');
    }

    public function index()
    {
        $data = array();

        $data['has_diana'] = customer_can('has_diana', false, false);
        $data['eventos'] = $this->cad_item_model->getPacoteEventos();

        $this->title = "Cockpit";
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Cockpit', '/cockpit/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'highcharts/highcharts.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('cockpit/default', $data);
    }

    public function getEventoPacote()
    {
        try {
            $eventoPacote = $this->cad_item_model->getPacoteEventos();

            return response_json(array(
                'status' => 200,
                'data' => $eventoPacote
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getEstabelecimentos()
    {
        try {
            $estabelecimentos = $this->cad_item_model->getEstabelecimentos();

            return response_json(array(
                'status' => 200,
                'data' => $estabelecimentos
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Status Itens" 
     */

    public function getStatusItens_old()
    {
        show_error('Página indisponível', 500, 'Erro');
        try {
            $this->homologacoes = $this->empresa_model->get_homologacao_by_id_empresa(sess_user_company());


            $empresa = $this->empresa_model->get_entry(sess_user_company());
            $campos_adicionais = explode("|", $empresa->campos_adicionais);
            $hasOwner = in_array('owner', $campos_adicionais);
            $hasTriagemOwner = in_array('triagem_owner', $campos_adicionais);
            $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));



            if ($hasOwner && $hasTriagemOwner) {
                $this->getItensAguardandoDefinicaoResponsavel();
            }


            $this->getItensEmAnalise();
            $this->getItensRespondidos();
            $this->getItensInformacoes();

            if ($hasOwner) {
                $this->getItensRevisarInformacoesTecnicas();
                $this->getItensRevisarInformacoesERP();
                $this->getItensInformacoesErpRevisadas();
            }

            if ($hasDescricaoGlobal) {
                $this->getItensAguardandoDescricao();
            }

            $this->getItensHomologacao();

            if ($hasOwner) {
                $this->getItensHomologadoEmRevisao();
            }
            $this->getItensRevisao();
            // $this->getItensAprovados();
            $this->getItensAprovadosV2();
            $this->getItensReprovados();


            return response_json(array(
                'status' => 200,
                'data' => $this->statusItens
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getItensEmAnalise()
    {
        // Itens que não estão na cad item e não possuem perguntas atreladas

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'sem_perguntas');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Em Análise",
            'quantidade' => $totalRows,
        );
    }

    public function getItensRespondidos()
    {
        // Itens que não estão na cad item e possuem somente perguntas respondidas

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Perguntas respondidas",
            'quantidade' => $totalRows,
        );
    }

    public function getItensRevisarInformacoesERP()
    {
        // Itens que não estão na cad item e possuem somente perguntas pendentes

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'revisar_informacoes_erp');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Revisar Informações ERP",
            'quantidade' => $totalRows,
        );
    }

    public function getItensInformacoes()
    {
        // Itens que não estão na cad item e possuem somente perguntas pendentes

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Pendente de Informações",
            'quantidade' => $totalRows,
        );
    }

    public function getItensHomologacao()
    {
        $status = "homologar";

        $this->cockpit_model->set_state('filter.list_opt', $status);

        $totalRows = $this->cockpit_model->getItensAprovadosV2();

        $this->statusItens[] = array(
            'descricao'  => "Em Homologação",
            'quantidade' => $totalRows,
        );
    }

    public function getItensRevisao()
    {
        $status = "revisao";

        $this->cockpit_model->set_state('filter.list_opt', $status);

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Em Revisão",
            'quantidade' => $totalRows,
        );
    }

    public function getItensAprovados()
    {
        $status = "homologado";
        $this->applyDefaultFiltersCadItem($status);

        $totalRows = $this->cad_item_model->get_total_entries($this->homologacoes);

        $this->statusItens[] = array(
            'descricao'  => "Aprovados",
            'quantidade' => $totalRows,
        );
    }

    public function getItensAprovadosV2()
    {
        $status = "homologado";
        $this->cockpit_model->set_state('filter.list_opt', $status);

        $totalRows = $this->cockpit_model->getItensAprovadosV2();

        $this->statusItens[] = array(
            'descricao'  => "Aprovados",
            'quantidade' => $totalRows,
        );
    }

    public function getItensReprovados()
    {
        $status = "nao_homologado";
        $this->cockpit_model->set_state('filter.list_opt', $status);

        $totalRows = $this->cockpit_model->getItensAprovadosV2();

        $this->statusItens[] = array(
            'descricao'  => "Reprovados",
            'quantidade' => $totalRows,
        );
    }

    public function applyDefaultFiltersCadItem($status)
    {

        /* Filtro - Evento/Pacote **/
        if ($this->input->get('evento') && count($this->input->get('evento')) > 0) {
            $evento = $this->input->get('evento');

            if (is_array($evento) && $evento[0] != "") {
                if (empty($evento[0])) {
                    unset($evento[0]);
                }
                $this->cad_item_model->set_state('filter.evento', $evento);
                $this->cockpit_model->set_state('filter.evento', $evento);
            } else {
                $this->cad_item_model->unset_state('filter.evento');
                $this->cockpit_model->unset_state('filter.evento');
            }
        } else {
            $this->cad_item_model->unset_state('filter.evento');
            $this->cockpit_model->unset_state('filter.evento');
        }

        /* Filtro - Status [Análise, Informações, Homologação, Revisão, Aprovado, Reprovado] **/
        $this->cad_item_model->unset_state('filter.list_opt');
        $this->cad_item_model->set_state('filter.list_opt', $status);
        $this->cad_item_model->set_state('filter.habilitar_pr', false);

        /* Filtro - Atribuído para todos **/
        $this->cad_item_model->set_state('filter.atribuido_para', '-1');

        /* Filtro - Status Exportação selecionados **/
        $this->cad_item_model->set_state('filter.status_exportacao', $this->statusExportacao);

        /* Filtro - Status Implementação selecionados **/
        $this->cad_item_model->set_state('filter.status_implementacao', $this->statusImplementacao);

        /* Filtro - Empresa do usuário logado **/
        $this->cad_item_model->set_state('filter.id_empresa', sess_user_company());

        /* Filtro - Estabelecimento **/
        $this->cad_item_model->unset_state('filter.estabelecimento');
        $this->cad_item_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        // $this->cad_item_model->set_state_store_session(TRUE);
        // $this->cad_item_model->restore_state_from_session('filter.', 'get');
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "SLA" 
     */

    public function getSLA()
    {
        try {
            $this->sla = array(
                array(
                    'descricao'  => 'sla',
                    'quantidade' => 0
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->sla
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Diana Utilização" 
     */

    public function getDianaUtilizacaoOld()
    {
        show_error('Página indisponível', 500, 'Erro');
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Itens que possuem predição **/
            $diana = $this->cockpit_model->getItemsPredicao(1);
            /* Itens que não possuem predição **/
            $manual = $this->cockpit_model->getItemsPredicao(0);
            /* Itens em análise (retroativos - antes de haver opções geradas pela Diana) **/

            $analise = $this->cockpit_model->getItemsRetroativosDiana();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');

            $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');

            $pendente_informacoes = $this->cockpit_model->getItensEmInformacoesPendentes();
            $analise = $analise + $perguntas_respondidas + $pendente_informacoes;

            /* Itens que passaram pela Diana e foram homologados sem necessitar de confirmação do responsavel **/
            $itens_na = $this->cockpit_model->getItemsNa();

            $this->dianaUtilizacao = array(
                array(
                    'descricao'  => 'Diana',
                    'quantidade' => $diana
                ),
                array(
                    'descricao'  => 'Escolha Diferente Diana',
                    'quantidade' => $manual
                ),
                array(
                    'descricao'  => 'Pendente de Atribuição',
                    'quantidade' => $analise
                ),
                array(
                    'descricao'  => 'N/A',
                    'quantidade' => $itens_na
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->dianaUtilizacao
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Diana Acuracidade" 
     */

    public function getDianaAcuracidadeOld()
    {
        show_error('Página indisponível', 500, 'Erro');
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Itens que possuem predição, e a sugestão escolhida foi a primeira **/
            $primeiraSugestao = $this->cockpit_model->getItemsPredicao(1, 1);

            /* Itens que possuem predição, e a sugestão escolhida foi a segunda **/
            $segundaSugestao = $this->cockpit_model->getItemsPredicao(1, 2);

            /* Itens que possuem predição, e a sugestão escolhida foi a terceira **/
            $terceiraSugestao = $this->cockpit_model->getItemsPredicao(1, 3);

            /* Itens que não possuem predição **/
            $escolhaDiferenteDiana = $this->cockpit_model->getItemsPredicao(0);

            /* Itens em análise (retroativos - antes de haver opções geradas pela Diana) **/
            $analise = $this->cockpit_model->getItemsRetroativosDiana();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');

            $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');

            $pendente_informacoes = $this->cockpit_model->getItensEmInformacoesPendentes();
            $semInformacao = $analise + $perguntas_respondidas + $pendente_informacoes;

            /* Itens que passaram pela Diana e foram homologados sem necessitar de confirmação do responsavel **/
            $itens_na = $this->cockpit_model->getItemsNa();

            $this->dianaAcuracidade = array(
                array(
                    'descricao'  => '1º Sugestão',
                    'quantidade' => $primeiraSugestao
                ),
                array(
                    'descricao'  => '2º Sugestão',
                    'quantidade' => $segundaSugestao
                ),
                array(
                    'descricao'  => '3º Sugestão',
                    'quantidade' => $terceiraSugestao
                ),
                array(
                    'descricao'  => 'Escolha Diferente Diana',
                    'quantidade' => $escolhaDiferenteDiana
                ),
                array(
                    'descricao'  => 'Sem Informação',
                    'quantidade' => $semInformacao
                ),
                array(
                    'descricao'  => 'N/A',
                    'quantidade' => $itens_na
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->dianaAcuracidade
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Atribuição" 
     */

    public function getAtribuicaoOld()
    {
        show_error('Página indisponível', 500, 'Erro');
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            $this->homologacoes = $this->empresa_model->get_homologacao_by_id_empresa(sess_user_company());

            /* Itens atribuídos - Itens que existem na tabela item, que também estão na cad_item **/
            $atribuido = $this->cockpit_model->getItemsAtribuidos();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');

            $totalPendentes = $this->cockpit_model->getItensEmInformacoesPendentes();

            $this->cockpit_model->unset_state('filter.evento');
            $this->cockpit_model->unset_state('filter.estabelecimento');
            $this->cockpit_model->unset_state('filter.list_opt');

            $status = "homologado";

            // $this->applyDefaultFiltersCadItem($status);
            // $aprovados = $this->cad_item_model->get_total_entries($this->homologacoes);
            $this->cockpit_model->set_state('filter.list_opt', $status);
            $aprovados = $this->cockpit_model->getItensAprovadosV2($this->homologacoes);


            $status = "homologar";
            // $this->applyDefaultFiltersCadItem($status);
            // $homologacao = $this->cad_item_model->get_total_entries($this->homologacoes);
            $this->cockpit_model->set_state('filter.list_opt', $status);
            $homologacao = $this->cockpit_model->getItensAprovadosV2($this->homologacoes);

            $status = "nao_homologado";
            // $this->applyDefaultFiltersCadItem($status);   
            // $reprovados = $this->cad_item_model->get_total_entries($this->homologacoes);
            $this->cockpit_model->set_state('filter.list_opt', $status);
            $reprovados = $this->cockpit_model->getItensAprovadosV2($this->homologacoes);

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');

            $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $status = "revisao";
            $this->applyDefaultFiltersCadItem($status);

            $emRevisao = $this->cad_item_model->get_total_entries($this->homologacoes);
            $atribuido = $homologacao + $totalPendentes + $perguntas_respondidas + $aprovados + $reprovados;
            // $atribuido = $aprovados + $totalPendentes + $homologacao + $reprovados;

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'sem_perguntas');

            $naoAtribuido = $this->cockpit_model->getItensEmAnalise();
            // $naoAtribuido = $this->cockpit_model->getItemsRetroativos(true); 

            $this->atribuicao = array(
                array(
                    'descricao'  => 'Atribuído',
                    'quantidade' => $atribuido
                ),
                array(
                    'descricao'  => 'Em Revisão',
                    'quantidade' => $emRevisao
                ),
                array(
                    'descricao'  => 'Não Atribuído',
                    'quantidade' => $naoAtribuido
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->atribuicao
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "NCM Divergentes" 
     */

    public function getNcmDivergentes()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Ncm's divergentes **/
            // $alteradas = $this->cockpit_model->getNcmDivergentes(true); 
            $ncm_alteradas = 0;
            $ncm_mantidas = 0;
            $alteradasv2 = $this->cockpit_model->getNcmDivergentesV2(true);


            $ncm_alteradas = (is_numeric($alteradasv2->alterado) && $alteradasv2->alterado > 0) ? $alteradasv2->alterado : 0;
            $ncm_mantidas = (is_numeric($alteradasv2->mantido) && $alteradasv2->mantido > 0) ? $alteradasv2->mantido : 0;

            // foreach ($alteradas as $alterada)
            // {
            //     if (preg_replace("/[^0-9]/","", $alterada->ncm) != preg_replace("/[^0-9]/","", $alterada->ncm_proposto)  ) {
            //         $ncm_alteradas++;
            //     } else {
            //         $ncm_mantidas++;
            //     }
            // }

            /* Ncm's não divergentes **/
            $mantida = $this->cockpit_model->getNcmDivergentesV2(false);

            $ncm_mantidas = $ncm_mantidas + $mantida;
            $empty = $this->cockpit_model->getEmptyNcm();


            $this->ncmDivergentes = array(
                array(
                    'descricao'  => 'Alterada',
                    'quantidade' => (int)$ncm_alteradas
                ),
                array(
                    'descricao'  => 'Mantida',
                    'quantidade' => (int)$ncm_mantidas
                ),
                array(
                    'descricao'  => 'NCM vazia',
                    'quantidade' => (int)$empty
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->ncmDivergentes
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Perguntas e Respostas" 
     */

    public function getPerguntasRespostas()
    {
        $this->ctr_pendencias_pergunta_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            $perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasByEmpresa();

            $pendentes = $perguntas->pendentes;

            $respondidas = $perguntas->respondidas;

            $total = $perguntas->total;

            $this->perguntasRespostas = array(
                array(
                    'descricao'  => 'Pendentes',
                    'quantidade' => (int) $pendentes
                ),
                array(
                    'descricao'  => 'Respondidas',
                    'quantidade' => (int) $respondidas
                ),
                array(
                    'descricao'  => 'Total',
                    'quantidade' => (int) $total
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->perguntasRespostas
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getItensRevisarInformacoesTecnicas()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'revisar_informacoes_tecnicas');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Revisar Informações Técnicas",
            'quantidade' => $totalRows,
        );
    }

    public function getItensHomologadoEmRevisao()
    {
        // $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        // $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        // $this->cockpit_model->set_state('filter.list_opt', 'homologado_em_revisao');

        // $totalRows = $this->cockpit_model->getItensHomologadoEmRevisao();

        // $this->statusItens[] = array(
        //     'descricao'  => "Homologados em Revisão",
        //     'quantidade' => $totalRows,
        // );

        $status = "homologado_em_revisao";
        $this->cockpit_model->set_state('filter.list_opt', $status);

        $totalRows = $this->cockpit_model->getItensAprovadosV2();

        $this->statusItens[] = array(
            'descricao'  => "Homologados em Revisão",
            'quantidade' => $totalRows,
        );
    }

    public function getItensInformacoesErpRevisadas()
    {
        // Itens que não estão na cad item e possuem somente perguntas pendentes

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'informacoes_erp_revisadas');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Informações ERP Revisadas",
            'quantidade' => $totalRows,
        );
    }

    public function getItensAguardandoDefinicaoResponsavel()
    {
        // Itens que não estão na cad item e não possuem owner definido com status 13

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'aguardando_definicao_responsavel');

        $totalRows = $this->cockpit_model->getQtdItensSemCadItem();

        $this->statusItens[] = array(
            'descricao'  => "Aguardando Definição Responsável",
            'quantidade' => $totalRows,
        );
    }

    public function getItensAguardandoDescricao()
    {
        // Itens que não estão na cad item e não possuem descrição curta

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'aguardando_descricao');

        $totalRows = $this->cockpit_model->getItensAguardandoDescricao();

        $this->statusItens[] = array(
            'descricao'  => "Aguardando Descrição",
            'quantidade' => $totalRows,
        );
    }

    public function getResponsaveis()
    {
        $id_empresa_sess = sess_user_company();

        try {
            $responsaveis = $this->cad_item_wf_atributo_model->get_list_responsavel($id_empresa_sess);

            return response_json(array(
                'status' => 200,
                'data' => $responsaveis
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    private function extract_fields_from_cols($cols, $line_break = ' ', $label_key_override = 'label_exportar')
    {
        if (empty($cols)) {
            return array();
        }

        $fields = array();

        foreach ($cols as $col) {
            $label_key = 'label';

            if (!empty($label_key_override) && array_key_exists($label_key_override, $col)) {
                $label_key = $label_key_override;
            }

            if (!empty($line_break)) {
                $col[$label_key] = str_replace('<br>', $line_break, $col[$label_key]);
            }

            $fields[$col['field']] = $col[$label_key];
        }

        return $fields;
    }

    private function get_post_values()
    {
        return json_decode(file_get_contents('php://input'), true);
    }

    private function num2alpha($n)
    {
        for ($r = ""; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41) . $r;
        }

        return $r;
    }

    // private function formatDate($dateTime)
    // {
    //     $date = DateTime::createFromFormat('Y-m-d H:i:s', $dateTime);
    //     return $date ? $date->format('d/m/Y') : null;
    // }

    private function formatDate($dateTime)
    {
        if (empty($dateTime) || strlen($dateTime) < 10) {
            return null;
        }

        return substr($dateTime, 8, 2) . '/' . substr($dateTime, 5, 2) . '/' . substr($dateTime, 0, 4);
    }

    public function exportar_attrs_detalhado($dados, $logs, $campos_adicionais)
    {
        $hasOwner =  in_array('owner', $campos_adicionais);

        $status_map = [
            1 => 'Pendente de Homologação',
            2 => 'Homologado',
            3 => 'Reprovado',
            4 => 'Inativo',
            5 => 'Em Revisão',
            6 => 'Em Análise',
            7 => 'Pendente de Informações',
            8 => 'Perguntas Respondidas',
            9 => 'Revisar Informações ERP',
            10 => 'Homologado em Revisão',
            11 => 'Revisar Informações Técnicas',
            12 => 'Informações ERP Revisadas',
            13 => 'Aguardando definição responsável',
            14 => 'Aguardando Descrição',
            15 => 'Perguntas Respondidas (Novas)'
        ];

        $status_attr_map = [
            1 => 'Sem preenchimento',
            2 => 'Obrigatórios não preenchidos',
            3 => 'Opcionais não preenchidos',
            4 => 'Totalmente preenchidos'
        ];

        $status_wf_atributos_table = [
            1 => 'Item nacional',
            2 => 'Análise de atributos - Fiscal',
            3 => 'Preenchimento/Validação Engenharia',
            4 => 'Homologação da Classificação Fiscal',
            5 => 'Em revisão',
            6 => 'Em revisão por alteração no PUCOMEX',
            7 => 'Homologados'
        ];

        $status_wf_integracao_table = [
            1 => 'Pendente de integração',
            2 => 'Enviado ao sistema de COMEX',
            3 => 'Integrado com PUCOMEX com sucesso'
        ];

        ini_set('zlib.output_compression', 'Off');
        ini_set('output_buffering', 'Off');
        set_time_limit(0);
        ini_set('memory_limit', '-1');

        $filename = 'planilha_status_atributos_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = WriterEntityFactory::createXLSXWriter();
        $writer->openToBrowser($filename);

        $styleHeader = (new StyleBuilder())
            ->setFontBold()
            ->setFontSize(12)
            ->setFontColor(Color::WHITE)
            ->setShouldWrapText(false)
            ->setCellAlignment(CellAlignment::CENTER)
            ->setBackgroundColor(Color::BLUE)
            ->build();

        // --- Primeiro loop: guardar dados e encontrar o máximo de atributos ---
        $todosOsItens = [];
        $maxAtributos = 0;

        while ($item = $dados->unbuffered_row()) {

            if (!empty($item->atributos)) {
                $atributos_geral = explode('|:|', $item->atributos);

                $atributos_adicionados = [];
                $atributos = [];
                $atributos_ncm_data = []; // Array para armazenar dados da ncm_atributo

                foreach ($atributos_geral as $attr) {

                    $valores = explode('||', $attr);

                    if (empty(trim($valores[0])))
                        continue;

                    if (!in_array($valores[0], $atributos_adicionados)) {
                        $atributos[] = $valores[0];
                        $atributos[] = $valores[1];

                        // Extrair dados da ncm_atributo (separados por |||)
                        $ncm_data = [];
                        if (count($valores) > 2) {
                            $ncm_parts = explode('|||', $valores[2]);
                            if (count($ncm_parts) >= 13) {
                                $ncm_data = [
                                    'nome_apresentacao' => trim($ncm_parts[1]),
                                    'forma_preenchimento' => trim($ncm_parts[2]),
                                    'definicao' => trim($ncm_parts[3]),
                                    'tamanho_maximo' => trim($ncm_parts[4]),
                                    'orientacao_preenchimento' => trim($ncm_parts[5]),
                                    'modalidade' => trim($ncm_parts[6]),
                                    'obrigatorio' => trim($ncm_parts[7]),
                                    'data_inicio_vigencia' => trim($ncm_parts[8]),
                                    'data_fim_vigencia' => trim($ncm_parts[9]),
                                    'dominio' => trim($ncm_parts[10]),
                                    'objetivos' => trim($ncm_parts[11]),
                                    'multivalorado' => trim($ncm_parts[12]),
                                    'casas_decimais' => trim($ncm_parts[13])
                                ];
                            }
                        }
                        $atributos_ncm_data[] = $ncm_data;
                    }

                    $atributos_adicionados[] = $valores[0];
                }
            }

            // $pares = empty($item->atributos) ? [] : explode(',', $item->atributos);
            // $atributos = [];

            // foreach ($pares as $par) {
            //     list($codigo, $atributo) = explode(': ', $par);
            //     $atributos[] = $atributo;
            //     $atributos[] = $codigo == '' || $codigo == ' ' ? '(Vazio)' : $codigo;
            // }

            $item->__atributos = isset($atributos) ? $atributos : []; // salva atributos no item
            $item->__atributos_ncm_data = isset($atributos_ncm_data) ? $atributos_ncm_data : []; // salva dados da ncm_atributo
            $todosOsItens[] = $item;

            if (isset($atributos) && count($atributos) > $maxAtributos) {
                $maxAtributos = count($atributos);
            }
        }

        $headerBase = ['Part Number', 'Estabelecimento'];
        if ($hasOwner) {
            array_push($headerBase, 'Owner');
        }
        $headerBase = array_merge($headerBase, [
            'NCM',
            'Descrição Proposta Resumida',
            'Descrição Completa',
            'Responsável Fiscal',
            'Responsável Engenharia',
            'Evento',
            'Prioridade',
            'Data da Criação',
            'Data da Modificação',
            'Status de classificação fiscal',
            'Status de atributos',
            'Status de preenchimento de atributo',
            'Data da Homologação',
            'Status de integração'
        ]);

        $headerAtributos = [];
        for ($i = 1; $i <= ($maxAtributos / 2); $i++) {
            $headerAtributos[] = 'Atributo' . $i;
            $headerAtributos[] = 'Valor' . $i;
            $headerAtributos[] = 'Nome Apresentação' . $i;
            $headerAtributos[] = 'Forma Preenchimento' . $i;
            $headerAtributos[] = 'Definição' . $i;
            $headerAtributos[] = 'Tamanho Máximo' . $i;
            $headerAtributos[] = 'Orientação Preenchimento' . $i;
            $headerAtributos[] = 'Modalidade' . $i;
            $headerAtributos[] = 'Obrigatório' . $i;
            $headerAtributos[] = 'Data Início Vigência' . $i;
            $headerAtributos[] = 'Data Fim Vigência' . $i;
            $headerAtributos[] = 'Domínio' . $i;
            $headerAtributos[] = 'Objetivos' . $i;
            $headerAtributos[] = 'Multivalorado' . $i;
            $headerAtributos[] = 'Casas Decimais' . $i;
        }

        $header = array_merge($headerBase, $headerAtributos);
        $headerRow = WriterEntityFactory::createRowFromArray($header, $styleHeader);
        $writer->addRow($headerRow);

        // --- Buffer de logs para descrição completa ---
        $logsbuff = [];
        foreach ($logs->result() as $log) {
            $logsbuff[$log->part_number] = $log->descricao_completa;
        }

        // --- Segundo loop: gerar linhas ---
        foreach ($todosOsItens as $item) {
            $descricao_completa = isset($logsbuff[$item->part_number]) ? $logsbuff[$item->part_number] : '';

            // Status Fiscal
            $status_map = [
                1 => 'Pendente de Homologação',
                2 => 'Homologado',
                3 => 'Reprovado',
                4 => 'Inativo',
                5 => 'Em Revisão',
                6 => 'Em Análise',
                7 => 'Pendente de Informações',
                8 => 'Perguntas Respondidas',
                9 => 'Revisar Informações ERP',
                10 => 'Homologado em Revisão',
                11 => 'Revisar Informações Técnicas',
                12 => 'Informações ERP Revisadas',
                13 => 'Aguardando definição responsável',
                14 => 'Aguardando Descrição',
                15 => 'Perguntas Respondidas (Novas)'
            ];
            $status = isset($status_map[$item->status_fiscal]) ? $status_map[$item->status_fiscal] : '';

            // $status_attr_map = [
            //     1 => 'Sem preenchimento',
            //     2 => 'Obrigatórios não preenchidos',
            //     3 => 'Opcionais não preenchidos',
            //     4 => 'Totalmente preenchidos'
            // ];

            $status_attr = isset($status_attr_map[$item->status_attr]) ? $status_attr_map[$item->status_attr] : '';
            if ($item->status_attr == 0 || empty($item->status_attr)) {
                $status_attr = 'Sem preenchimento';
            }


            // Garante que a linha tenha o número certo de colunas de atributos
            $atributos = $item->__atributos;
            $atributos_ncm_data = $item->__atributos_ncm_data;

            // Monta array final com atributos + dados da ncm_atributo
            $atributos_completos = [];
            $num_atributos = count($atributos) / 2; // Cada atributo tem código e valor

            for ($i = 0; $i < $num_atributos; $i++) {
                $index_codigo = $i * 2;
                $index_valor = $i * 2 + 1;

                // Adiciona código e valor do atributo
                $atributos_completos[] = isset($atributos[$index_codigo]) ? $atributos[$index_codigo] : '';
                $atributos_completos[] = isset($atributos[$index_valor]) ? $atributos[$index_valor] : '';

                // Adiciona dados da ncm_atributo para este atributo
                if (isset($atributos_ncm_data[$i])) {
                    $ncm_data = $atributos_ncm_data[$i];
                    $atributos_completos[] = $ncm_data['nome_apresentacao'] ?? '';
                    $atributos_completos[] = $ncm_data['forma_preenchimento'] ?? '';
                    $atributos_completos[] = $ncm_data['definicao'] ?? '';
                    $atributos_completos[] = $ncm_data['tamanho_maximo'] ?? '';
                    $atributos_completos[] = $ncm_data['orientacao_preenchimento'] ?? '';
                    $atributos_completos[] = $ncm_data['modalidade'] ?? '';
                    $atributos_completos[] = $ncm_data['obrigatorio'] ?? '';
                    $atributos_completos[] = $ncm_data['data_inicio_vigencia'] ?? '';
                    $atributos_completos[] = $ncm_data['data_fim_vigencia'] ?? '';
                    $atributos_completos[] = $ncm_data['dominio'] ?? '';
                    $atributos_completos[] = $ncm_data['objetivos'] ?? '';
                    $atributos_completos[] = $ncm_data['multivalorado'] ?? '';
                    $atributos_completos[] = $ncm_data['casas_decimais'] ?? '';
                } else {
                    // Preenche com vazios se não há dados da ncm_atributo
                    for ($j = 0; $j < 13; $j++) {
                        $atributos_completos[] = '';
                    }
                }
            }

            // Calcula quantas colunas devem ter no total (15 colunas por atributo: código + valor + 13 campos ncm)
            $colunas_por_atributo = 15;
            $max_colunas_atributos = ($maxAtributos / 2) * $colunas_por_atributo;

            while (count($atributos_completos) < $max_colunas_atributos) {
                $atributos_completos[] = ''; // Preenche com vazio
            }

            //--------

            $status_classificacao_fiscal = isset($status_map[$item->status_classificacao_fiscal]) ? $status_map[$item->status_classificacao_fiscal] : '';

            $status_atributos = isset($status_wf_atributos_table[$item->status_atributo]) ? $status_wf_atributos_table[$item->status_atributo] : '';

            $status_integracao = isset($status_wf_integracao_table[$item->status_integracao]) ? $status_wf_integracao_table[$item->status_integracao] : 'Pendente de integração';

            $row = [$item->part_number, $item->estabelecimento];
            if ($hasOwner) {
                array_push($row, $item->owner_descricao);
            }
            $row = array_merge($row, [
                $item->ncm_proposto,
                $item->descricao_mercado_local,
                $descricao_completa,
                $item->resp_fiscal,
                $item->resp_engenharia,
                $item->evento,
                $item->id_prioridade,
                $this->formatDate($item->dat_criacao),
                $this->formatDate($item->data_modificacao),
                $status_classificacao_fiscal,
                $status_atributos,
                $status_attr,
                $this->formatDate($item->data_homologacao),
                $status_integracao
            ], $atributos_completos);

            $rowData = WriterEntityFactory::createRowFromArray($row);

            $writer->addRow($rowData);
        }

        $writer->close();
        exit;
    }


    public function exportar_attrs($dados, $campos_adicionais)
    {

        $hasOwner =  in_array('owner', $campos_adicionais);

        $status_map = [
            1 => 'Pendente de Homologação',
            2 => 'Homologado',
            3 => 'Reprovado',
            4 => 'Inativo',
            5 => 'Em Revisão',
            6 => 'Em Análise',
            7 => 'Pendente de Informações',
            8 => 'Perguntas Respondidas',
            9 => 'Revisar Informações ERP',
            10 => 'Homologado em Revisão',
            11 => 'Revisar Informações Técnicas',
            12 => 'Informações ERP Revisadas',
            13 => 'Aguardando definição responsável',
            14 => 'Aguardando Descrição',
            15 => 'Perguntas Respondidas (Novas)'
        ];

        $status_attr_map = [
            1 => 'Sem preenchimento',
            2 => 'Obrigatórios não preenchidos',
            3 => 'Opcionais não preenchidos',
            4 => 'Totalmente preenchidos'
        ];

        $status_wf_atributos_table = [
            1 => 'Item nacional',
            2 => 'Análise de atributos - Fiscal',
            3 => 'Preenchimento/Validação Engenharia',
            4 => 'Homologação da Classificação Fiscal',
            5 => 'Em revisão',
            6 => 'Em revisão por alteração no PUCOMEX',
            7 => 'Homologados'
        ];

        $status_wf_integracao_table = [
            1 => 'Pendente de integração',
            2 => 'Enviado ao sistema de COMEX',
            3 => 'Integrado com PUCOMEX com sucesso'
        ];

        ini_set('zlib.output_compression', 'Off');
        ini_set('output_buffering', 'Off');
        set_time_limit(0);
        ini_set('memory_limit', '-1');

        $filename = 'planilha_status_atributos_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Configurar headers para download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // Criar o writer
        $writer = WriterEntityFactory::createXLSXWriter();
        $writer->openToBrowser($filename); // Escreve diretamente na saída

        $styleHeader = (new StyleBuilder())
            ->setFontBold()
            ->setFontSize(12)
            ->setFontColor(Color::WHITE)
            ->setShouldWrapText(false)
            ->setCellAlignment(CellAlignment::CENTER)
            ->setBackgroundColor(Color::BLUE)
            ->build();

        $cabecalho = ['Part Number', 'Estabelecimento'];
        if ($hasOwner) {
            array_push($cabecalho, 'Owner');
        }
        $cabecalho = array_merge($cabecalho, [
            'NCM',
            'Descrição Proposta Resumida',
            'Responsável Fiscal',
            'Responsável Engenharia',
            'Evento',
            'Prioridade',
            'Data da Criação',
            'Data da Modificação',
            'Status de classificação fiscal',
            'Status de atributos',
            'Status de preenchimento de atributo',
            'Status de integração de atributo'
        ]);

        $headerRow = WriterEntityFactory::createRowFromArray($cabecalho, $styleHeader);

        $writer->addRow($headerRow);

        // Dados
        while ($item = $dados->unbuffered_row()) {
            $status = $this->getStatusDescription($item->status_attr);

            $status_classificacao_fiscal = isset($status_map[$item->status_classificacao_fiscal]) ? $status_map[$item->status_classificacao_fiscal] : '';

            $status_atributos = isset($status_wf_atributos_table[$item->status_atributo]) ? $status_wf_atributos_table[$item->status_atributo] : '';

            $status_integracao = isset($status_wf_integracao_table[$item->status_integracao]) ? $status_wf_integracao_table[$item->status_integracao] : 'Pendente de integração';

            //  $status_attr = isset($status_attr_map[$item->status_atributos]) ? $status_attr_map[$item->status_atributos] : '';


            $row = [$item->part_number, $item->estabelecimento];
            if ($hasOwner) {
                array_push($row, $item->owner_descricao);
            }
            $row = array_merge($row, [
                $item->ncm_proposto,
                $item->descricao_mercado_local,
                $item->resp_fiscal,
                $item->resp_engenharia,
                $item->evento,
                $item->id_prioridade,
                $this->formatDate($item->dat_criacao),
                $this->formatDate($item->data_modificacao),
                $status_classificacao_fiscal,
                $status_atributos,
                $status,
                $status_integracao
            ]);



            $rowData = WriterEntityFactory::createRowFromArray($row);

            $writer->addRow($rowData);
        }

        $writer->close();
        exit;
    }

    // Função auxiliar para mapear status
    private function getStatusDescription($statusCode)
    {
        switch ((int)$statusCode) {
            case 1:
                return 'Sem preenchimento';
            case 2:
                return 'Obrigatórios não preenchidos';
            case 3:
                return 'Opcionais não preenchidos';
            case 4:
                return 'Totalmente preenchidos';
            default:
                return 'Sem preenchimento';
        }
    }

    public function exportar_consult($dados, $campos_adicionais = null, $mes = null, $ano = null)
    {
        $this->load->library('Excel');

        $this->excel->setActiveSheetIndex(0);
        $this->excel->getActiveSheet()->setTitle('Visão de classificação');
        $this->excel->getActiveSheet()->setCellValue('A1', 'Mês de referência');
        $this->excel->getActiveSheet()->setCellValue('B1', 'Ano de referência');
        $this->excel->getActiveSheet()->setCellValue('C1', 'Consultor');
        $this->excel->getActiveSheet()->setCellValue('D1', 'Empresa');
        $this->excel->getActiveSheet()->setCellValue('E1', 'Part Number');
        $this->excel->getActiveSheet()->setCellValue('F1', 'Estabelecimento');
        $this->excel->getActiveSheet()->setCellValue('G1', 'Descrição proposta resumida');
        $this->excel->getActiveSheet()->setCellValue('H1', 'Data de disponibilização por consultor');

        $this->excel->getActiveSheet()->getStyle('A1:H1')->getFont()->setBold(true);
        $this->excel->getActiveSheet()->getStyle('A1:H1')->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');

        $this->excel->getActiveSheet()->getStyle('A1:H1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);


        $this->excel->getActiveSheet()->getColumnDimension('A')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('B')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('C')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('D')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('E')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('F')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('G')->setWidth('80');
        $this->excel->getActiveSheet()->getColumnDimension('H')->setWidth('50');

        if (!empty($dados)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );

            $row = 2; // Começa na linha 2 (a linha 1 é o cabeçalho)

            foreach ($dados as $k => $item) {

                if (!empty($item->dat_disp_homologacao)) {
                    $dataFormatada = DateTime::createFromFormat('Y-m-d H:i:s', $item->dat_disp_homologacao)->format('d/m/Y H:i:s');
                } else {
                    $dataFormatada = null;
                }

                $nome = !empty($item->nome) ? $item->nome : '';
                $nome_empresa = !empty($item->nome_empresa) ? $item->nome_empresa : '';
                $part_number = !empty($item->part_number) ? $item->part_number : '';
                $estabelecimento = !empty($item->estabelecimento) ? $item->estabelecimento : '';
                $descricao_mercado_local = !empty($item->descricao_mercado_local) ? $item->descricao_mercado_local : '';

                $this->excel->getActiveSheet()->setCellValueExplicit('A' . $row, $mes, PHPExcel_Cell_DataType::TYPE_STRING);
                $this->excel->getActiveSheet()->setCellValueExplicit('B' . $row, $ano, PHPExcel_Cell_DataType::TYPE_STRING);


                $this->excel->getActiveSheet()->setCellValueExplicit('C' . $row, $nome, PHPExcel_Cell_DataType::TYPE_STRING);
                $this->excel->getActiveSheet()->setCellValueExplicit('D' . $row, $nome_empresa, PHPExcel_Cell_DataType::TYPE_STRING);
                $this->excel->getActiveSheet()->setCellValueExplicit('E' . $row, $part_number, PHPExcel_Cell_DataType::TYPE_STRING);
                $this->excel->getActiveSheet()->setCellValueExplicit('F' . $row, $estabelecimento, PHPExcel_Cell_DataType::TYPE_STRING);
                $this->excel->getActiveSheet()->setCellValueExplicit('G' . $row, $descricao_mercado_local, PHPExcel_Cell_DataType::TYPE_STRING);
                $this->excel->getActiveSheet()->setCellValueExplicit('H' . $row, $dataFormatada, PHPExcel_Cell_DataType::TYPE_STRING);

                $row++;
            }


            $filename = 'consultores_' . date('Y-m-d_H-i-s') . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            // header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');
        }
    }


    public function downloadReportConsult()
    {
        set_time_limit(0);
        ini_set('memory_limit', -1);
        $id_empresa = sess_user_company();

        $this->load->model(
            'item_model'
        );
        $this->load->model(
            'empresa_model'
        );
        $statusConsult = [];
        if ($cockpit_mes = $this->input->post('status_mes')) {
            switch ($cockpit_mes) {

                case 'Janeiro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 1);
                    break;
                case 'Fevereiro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 2);
                    break;
                case 'Março':
                    $this->item_model->set_state('filter.cockpit_status_mes', 3);
                    break;
                case 'Abril':
                    $this->item_model->set_state('filter.cockpit_status_mes', 4);
                    break;
                case 'Maio':
                    $this->item_model->set_state('filter.cockpit_status_mes', 5);
                    break;
                case 'Junho':
                    $this->item_model->set_state('filter.cockpit_status_mes', 6);
                    break;
                case 'Julho':
                    $this->item_model->set_state('filter.cockpit_status_mes', 7);
                    break;
                case 'Agosto':
                    $this->item_model->set_state('filter.cockpit_status_mes', 8);
                    break;
                case 'Setembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 9);
                    break;
                case 'Outubro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 10);
                    break;
                case 'Novembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 11);
                    break;
                case 'Dezembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 12);
                    break;
                default:
                    $this->item_model->set_state('filter.cockpit_status_mes', 1);
                    break;
            }
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_mes');
        }
        $post = $this->get_post_values();

        if ($cockpit_mes = $post['status_mes']) {
            switch ($cockpit_mes) {

                case 'Janeiro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 1);
                    break;
                case 'Fevereiro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 2);
                    break;
                case 'Março':
                    $this->item_model->set_state('filter.cockpit_status_mes', 3);
                    break;
                case 'Abril':
                    $this->item_model->set_state('filter.cockpit_status_mes', 4);
                    break;
                case 'Maio':
                    $this->item_model->set_state('filter.cockpit_status_mes', 5);
                    break;
                case 'Junho':
                    $this->item_model->set_state('filter.cockpit_status_mes', 6);
                    break;
                case 'Julho':
                    $this->item_model->set_state('filter.cockpit_status_mes', 7);
                    break;
                case 'Agosto':
                    $this->item_model->set_state('filter.cockpit_status_mes', 8);
                    break;
                case 'Setembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 9);
                    break;
                case 'Outubro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 10);
                    break;
                case 'Novembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 11);
                    break;
                case 'Dezembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 12);
                    break;
                default:
                    $this->item_model->set_state('filter.cockpit_status_mes', 1);
                    break;
            }
        }

        if ($post['status_ano']) {
            $this->item_model->set_state('filter.cockpit_status_ano', $post['status_ano']);
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_ano');
        }
        if ($post['status_consultor']) {
            $this->item_model->set_state('filter.cockpit_status_consultor', $post['status_consultor']);
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_consultor');
        }
        if ($post['status_empresa']) {
            $this->item_model->set_state('filter.cockpit_status_empresa', $post['status_empresa']);
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_empresa');
        }

        $item = $this->item_model->get_status_consult_report();

        $empresa = $this->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode("|", $empresa->campos_adicionais);


        return  $this->exportar_consult($item, null, $cockpit_mes, $post['status_ano']);
    }


    public function getStatusAtributos()
    {
        set_time_limit(0);
        ini_set('memory_limit', -1);
        $post = $this->get_post_values();

        $exportar = $post['export'];
        $id_empresa = sess_user_company();
        try {

            if (!empty($exportar) && $exportar == 'Detalhado') {

                $this->load->model('item_pais_model');
                $logs = $this->item_pais_model->get_entries_for_xls($id_empresa);
            }
            if ($this->input->get('status_attr')) {
                $this->cockpit_model->set_state('filter.status_attr', $this->input->get('status_attr'));
            }
            if ($this->input->get('status_classificacao')) {
                $this->cockpit_model->set_state('filter.status_classificacao', $this->input->get('status_classificacao'));
            }
            if ($this->input->get('status_preenchimento')) {
                $this->cockpit_model->set_state('filter.status_preenchimento', $this->input->get('status_preenchimento'));
            }
            if ($this->input->get('prioridade')) {
                $this->cockpit_model->set_state('filter.prioridade', $this->input->get('prioridade'));
            }
            if ($this->input->get('ncm_proposto')) {
                $this->cockpit_model->set_state('filter.ncm_proposto', $this->input->get('ncm_proposto'));
            }
            if ($this->input->get('status_integracao')) {
                $this->cockpit_model->set_state('filter.status_integracao', $this->input->get('status_integracao'));
            }
            if ($this->input->get('objetivo')) {
                $this->cockpit_model->set_state('filter.objetivos', $this->input->get('objetivo'));
            }


            if ($this->input->get('evento')) {
                $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            }
            if ($this->input->get('estabelecimento')) {
                $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            }
            if ($this->input->get('responsaveis')) {
                $this->cockpit_model->set_state('filter.responsavel', $this->input->get('responsaveis'));
            }
            if ($this->input->get('search')) {
                $this->cockpit_model->set_state('filter.search', $this->input->get('search'));
            }
            //------------
            if ($post['evento']) {
                $this->cockpit_model->set_state('filter.evento', $post['evento']);
            }
            if ($post['estabelecimento']) {
                $this->cockpit_model->set_state('filter.estabelecimento', $post['estabelecimento']);
            }
            if ($post['responsaveis']) {
                $this->cockpit_model->set_state('filter.responsavel', $post['responsaveis']);
            }
            if ($post['search']) {
                $this->cockpit_model->set_state('filter.search', $post['search']);
            }

            if ($post['status_attr']) {
                $this->cockpit_model->set_state('filter.status_attr', $post['status_attr']);
            }
            if ($post['status_classificacao']) {
                $this->cockpit_model->set_state('filter.status_classificacao', $post['status_classificacao']);
            }
            if ($post['status_preenchimento']) {
                $this->cockpit_model->set_state('filter.status_preenchimento', $post['status_preenchimento']);
            }
            if ($post['prioridade']) {
                $this->cockpit_model->set_state('filter.prioridade', $post['prioridade']);
            }
            if ($post['ncm_proposto']) {
                $this->cockpit_model->set_state('filter.ncm_proposto', $post['ncm_proposto']);
            }
            if ($post['status_integracao']) {
                $this->cockpit_model->set_state('filter.status_integracao', $post['status_integracao']);
            }
            if ($post['objetivo']) {
                $this->cockpit_model->set_state('filter.objetivos', $post['objetivo']);
            }

            if ($exportar) {
                set_time_limit(0);
                ini_set('memory_limit', -1);
                $id_empresa = sess_user_company();

                $this->load->model('empresa_model');

                $empresa = $this->empresa_model->get_entry($id_empresa);
                $campos_adicionais = explode("|", $empresa->campos_adicionais);

                $dados = $this->cad_item_wf_atributo_model->get_count_attrs($exportar);
                ddJson($dados->result());

                if ($exportar == 'Sintetico') {
                    return  $this->exportar_attrs($dados, $campos_adicionais);
                } else if ($exportar && $exportar == 'Detalhado') {
                    return $this->exportar_attrs_detalhado($dados, $logs, $campos_adicionais);
                }
            } else {
                $dados = $this->cad_item_wf_atributo_model->get_count_attrs($exportar);
            }
            $quantidade_itens = $dados->row();

            $this->statusItens[] = array(
                'descricao'  => "Sem Preenchimento",
                'quantidade' => (int) !empty($quantidade_itens->sem_preenchimento) ? (int) $quantidade_itens->sem_preenchimento : 0,
            );

            $this->statusItens[] = array(
                'descricao'  => "Obrigatórios não preenchidos",
                'quantidade' => (int) !empty($quantidade_itens->obrigatorio_nao_preenchido) ? (int) $quantidade_itens->obrigatorio_nao_preenchido : 0,
            );

            $this->statusItens[] = array(
                'descricao'  => "Opcionais não preenchidos",
                'quantidade' => (int) !empty($quantidade_itens->opcional_nao_preenchido) ? (int) $quantidade_itens->opcional_nao_preenchido : 0,
            );

            $this->statusItens[] = array(
                'descricao'  => "Totalmente preenchidos",
                'quantidade' => (int) !empty($quantidade_itens->totalmente_preenchido) ? (int) $quantidade_itens->totalmente_preenchido : 0
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->statusItens
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getStatusAttrs()
    {
        $this->load->library("Item/Atributo");

        return response_json([
            "data"  => $this->atributo->get_all_status()
        ], 200);
    }

    public function getStatusClass()
    {
        $this->load->model(
            'item_model'
        );
        return response_json([
            "data"  => $this->item_model->get_status()
        ], 200);
    }

    public function getStatusPreenchimento()
    {
        return response_json([
            "data"  => [
                [
                    "id" => 1,
                    "descricao" => "Sem preenchimento"
                ],
                [
                    "id" => 2,
                    "descricao" => "Atributos obrigatórios não preenchidos"
                ],
                [
                    "id" => 3,
                    "descricao" => "Atributos opcionais não preenchidos"
                ],
                [
                    "id" => 4,
                    "descricao" => "Totalmente preenchidos"
                ]
            ]
        ], 200);
    }

    public function getStatusIntegracao()
    {
        $this->load->library("Item/Atributo");
        return response_json([
            "data"  => $this->atributo->get_all_status_integracao()
        ], 200);
    }

    public function getOwner()
    {
        $this->load->model(
            'empresa_model'
        );
        return response_json([
            "data"  => $this->empresa_model->get_owners_by_empresa($empresa)
        ], 200);
    }

    public function getStatusAnos()
    {
        $anos = array();
        $anoAtual = date('Y');

        for ($i = 0; $i < 10; $i++) {
            $anos[] = $anoAtual - $i;
        }

        return response_json([
            "data"  => $anos
        ], 200);
    }

    public function getStatusMes()
    {
        return response_json([
            "data"  => ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro']
        ], 200);
    }

    public function getStatusEmpresa()
    {
        $this->load->model(
            'empresa_model'
        );
        return response_json([
            "data"  => $this->empresa_model->get_all_entries()
        ], 200);
    }

    public function getStatusConsultor()
    {
        $this->load->model(
            'item_model'
        );
        return response_json([
            "data"  => $this->item_model->get_list_consultor()
        ], 200);
    }


    public function getObjetivos()
    {
        $this->load->model(
            'empresa_model'
        );
        return response_json([
            "data"  => $this->cad_item_wf_atributo_model->get_list_objetivos()
        ], 200);
    }

    public function getPrioridade()
    {
        $id_empresa_sess = sess_user_company();
        $this->load->model(
            'empresa_prioridades_model'
        );
        return response_json([
            "data"  => $this->empresa_prioridades_model->get_entry($id_empresa_sess)
        ], 200);
    }

    public function getNcm()
    {
        $id_empresa_sess = sess_user_company();
        $this->load->model(
            'empresa_prioridades_model'
        );
        return response_json([
            "data"  => $this->cad_item_wf_atributo_model->get_ncm($id_empresa_sess)
        ], 200);
    }

    public function getStatusConsult()
    {
        $this->load->model(
            'item_model'
        );
        $this->load->model(
            'empresa_model'
        );

        if ($cockpit_mes = $this->input->get('status_mes')) {
            switch ($cockpit_mes) {

                case 'Janeiro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 1);
                    break;
                case 'Fevereiro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 2);
                    break;
                case 'Março':
                    $this->item_model->set_state('filter.cockpit_status_mes', 3);
                    break;
                case 'Abril':
                    $this->item_model->set_state('filter.cockpit_status_mes', 4);
                    break;
                case 'Maio':
                    $this->item_model->set_state('filter.cockpit_status_mes', 5);
                    break;
                case 'Junho':
                    $this->item_model->set_state('filter.cockpit_status_mes', 6);
                    break;
                case 'Julho':
                    $this->item_model->set_state('filter.cockpit_status_mes', 7);
                    break;
                case 'Agosto':
                    $this->item_model->set_state('filter.cockpit_status_mes', 8);
                    break;
                case 'Setembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 9);
                    break;
                case 'Outubro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 10);
                    break;
                case 'Novembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 11);
                    break;
                case 'Dezembro':
                    $this->item_model->set_state('filter.cockpit_status_mes', 12);
                    break;
                default:
                    $this->item_model->set_state('filter.cockpit_status_mes', 1);
                    break;
            }
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_mes');
        }
        if ($this->input->get('status_ano')) {
            $this->item_model->set_state('filter.cockpit_status_ano', $this->input->get('status_ano'));
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_ano');
        }
        if ($this->input->get('status_consultor')) {
            $this->item_model->set_state('filter.cockpit_status_consultor', $this->input->get('status_consultor'));
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_consultor');
        }
        if ($this->input->get('status_empresa')) {
            $this->item_model->set_state('filter.cockpit_status_empresa', $this->input->get('status_empresa'));
        } else {
            $this->cad_item_model->unset_state('filter.cockpit_status_empresa');
        }

        $item = $this->item_model->get_status_consult();
        if (!empty($item)) {
            foreach ($item as $i) {
                $this->statusConsult[] = array(
                    'descricao'  => $i->nome,
                    'quantidade' => (int) (isset($i->qtd_itens) ? $i->qtd_itens : 0),
                );
            }
        } else {
            $this->statusConsult[] = array(
                'descricao'  => 'Nenhum item encontrado',
                'quantidade' => 0
            );
        }

        return response_json(array(
            'status' => 200,
            'data' => $this->statusConsult
        ), 200);
    }

    public function getStatusItens()
    {
        try {
            $this->homologacoes = $this->empresa_model->get_homologacao_by_id_empresa(sess_user_company());


            $empresa = $this->empresa_model->get_entry(sess_user_company());
            $campos_adicionais = explode("|", $empresa->campos_adicionais);
            $hasOwner = in_array('owner', $campos_adicionais);
            $hasTriagemOwner = in_array('triagem_owner', $campos_adicionais);
            $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

            $icadFormatado = $this->cockpit_model->getStatusItensCockpitData();
            $incadFormatado = $this->cockpit_model->getStatusItensCockpitData(true);
            $qtdColumns = 7;
            $hasAbreviado = false;

            if ($hasOwner) {
                $qtdColumns += 5;
                if ($hasTriagemOwner)
                    $qtdColumns += 1;
            }
            if ($hasDescricaoGlobal) {
                $qtdColumns += 1;
            }

            if ($qtdColumns >= 12) {
                $hasAbreviado = true;
            }

            if ($hasOwner && $hasTriagemOwner) {
                $this->statusItens[] = array(
                    'descricao'  => ($hasAbreviado) ? "Aguard. Defin. Responsável" : "Aguardando Definição Responsável",
                    'quantidade' => (int) (isset($incadFormatado['aguardando_definicao_responsavel']) ? $incadFormatado['aguardando_definicao_responsavel'] : 0),
                );
            }

            $this->statusItens[] = array(
                'descricao'  => "Em Análise",
                'quantidade' => (int) (isset($incadFormatado['em_analise']) ? $incadFormatado['em_analise'] : 0),
            );
            if ($hasDescricaoGlobal) {
                $this->statusItens[] = array(
                    'descricao'  => "Aguardando Descrição",
                    'quantidade' => (int) (isset($incadFormatado['aguardando_descricao']) ? $incadFormatado['aguardando_descricao'] : 0),
                );
            }
            $this->statusItens[] = array(
                'descricao'  => "Pendente de Informações",
                'quantidade' => (int) (isset($incadFormatado['pendente_duvidas']) ? $incadFormatado['pendente_duvidas'] : 0),
            );
            $this->statusItens[] = array(
                'descricao'  => ($hasAbreviado) ? "Perg. Respondidas" : "Perguntas Respondidas",
                'quantidade' => (int) (isset($incadFormatado['respondido']) ? $incadFormatado['respondido'] : 0),
            );

            if ($hasOwner) {
                $this->statusItens[] = array(
                    'descricao'  => ($hasAbreviado) ? "Revisar Info. Técnicas" : "Revisar Informações Técnicas",
                    'quantidade' => (int) (isset($incadFormatado['revisar_informacoes_tecnicas']) ? $incadFormatado['revisar_informacoes_tecnicas'] : 0)
                );

                $this->statusItens[] = array(
                    'descricao'  => ($hasAbreviado) ? "Perg. Respond. (Novas)" : "Perguntas Respondidas (Novas)",
                    'quantidade' => (int) (isset($incadFormatado['perguntas_respondidas_novas']) ? $incadFormatado['perguntas_respondidas_novas'] : 0),
                );

                $this->statusItens[] = array(
                    'descricao'  => ($hasAbreviado) ? "Revisar Info. ERP" : "Revisar Informações ERP",
                    'quantidade' => (int) (isset($incadFormatado['revisar_informacoes_erp']) ? $incadFormatado['revisar_informacoes_erp'] : 0),
                );

                $this->statusItens[] = array(
                    'descricao'  => ($hasAbreviado) ? "Info. ERP Revisadas" : "Informações ERP Revisadas",
                    'quantidade' => (int) (isset($incadFormatado['informacoes_erp_revisadas']) ? $incadFormatado['informacoes_erp_revisadas'] : 0),
                );
            }

            $this->statusItens[] = array(
                'descricao'  => ($hasAbreviado) ? "Em Homol." : "Em Homologação",
                'quantidade' => (int) (isset($icadFormatado['homologar']) ? $icadFormatado['homologar'] : 0),
            );

            if ($hasOwner) {
                $this->statusItens[] = array(
                    'descricao'  => ($hasAbreviado) ? "Homol. em Revisão" : "Homologados em Revisão",
                    'quantidade' => (int) (isset($icadFormatado['homologado_em_revisao']) ? $icadFormatado['homologado_em_revisao'] : 0),
                );
            }
            $this->statusItens[] = array(
                'descricao'  => "Em Revisão",
                'quantidade' => (int) (isset($icadFormatado['revisao']) ? $icadFormatado['revisao'] : 0),
            );
            // $this->getItensAprovados();
            $this->statusItens[] = array(
                'descricao'  => ($hasAbreviado) ? "Homolog." : "Homologados",
                'quantidade' => (int) (isset($icadFormatado['homologado']) ? $icadFormatado['homologado'] : 0),
            );
            $this->statusItens[] = array(
                'descricao'  => "Reprovados",
                'quantidade' => (int) (isset($icadFormatado['nao_homologado']) ? $icadFormatado['nao_homologado'] : 0),
            );
            $this->statusItens[] = array(
                'descricao'  => "Inativos",
                'quantidade' => (int) (isset($icadFormatado['obsoleto']) ? $icadFormatado['obsoleto'] : 0),
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->statusItens
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getDianaUtilizacao()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Itens que possuem predição **/
            $diana = $this->cockpit_model->getItemsPredicao(1);
            /* Itens que não possuem predição **/
            $manual = $this->cockpit_model->getItemsPredicao(0);

            $analise = $this->cockpit_model->getDianaUtilizacaoAnaliseCockpitData();

            /* Itens que passaram pela Diana e foram homologados sem necessitar de confirmação do responsavel **/
            $itens_na = $this->cockpit_model->getItemsNa();

            $this->dianaUtilizacao = array(
                array(
                    'descricao'  => 'Diana',
                    'quantidade' => $diana
                ),
                array(
                    'descricao'  => 'Escolha Diferente Diana',
                    'quantidade' => $manual
                ),
                array(
                    'descricao'  => 'Pendente de Atribuição',
                    'quantidade' => $analise
                ),
                array(
                    'descricao'  => 'N/A',
                    'quantidade' => $itens_na
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->dianaUtilizacao
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getDianaAcuracidade()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Itens que possuem predição, e a sugestão escolhida foi a primeira **/
            $primeiraSugestao = $this->cockpit_model->getItemsPredicao(1, 1);

            /* Itens que possuem predição, e a sugestão escolhida foi a segunda **/
            $segundaSugestao = $this->cockpit_model->getItemsPredicao(1, 2);

            /* Itens que possuem predição, e a sugestão escolhida foi a terceira **/
            $terceiraSugestao = $this->cockpit_model->getItemsPredicao(1, 3);

            /* Itens que não possuem predição **/
            $escolhaDiferenteDiana = $this->cockpit_model->getItemsPredicao(0);

            $semInformacao = $this->cockpit_model->getDianaUtilizacaoAnaliseCockpitData();

            /* Itens que passaram pela Diana e foram homologados sem necessitar de confirmação do responsavel **/
            $itens_na = $this->cockpit_model->getItemsNa();

            $this->dianaAcuracidade = array(
                array(
                    'descricao'  => '1º Sugestão',
                    'quantidade' => $primeiraSugestao
                ),
                array(
                    'descricao'  => '2º Sugestão',
                    'quantidade' => $segundaSugestao
                ),
                array(
                    'descricao'  => '3º Sugestão',
                    'quantidade' => $terceiraSugestao
                ),
                array(
                    'descricao'  => 'Escolha Diferente Diana',
                    'quantidade' => $escolhaDiferenteDiana
                ),
                array(
                    'descricao'  => 'Sem Informação',
                    'quantidade' => $semInformacao
                ),
                array(
                    'descricao'  => 'N/A',
                    'quantidade' => $itens_na
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->dianaAcuracidade
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getAtribuicao()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            $this->homologacoes = $this->empresa_model->get_homologacao_by_id_empresa(sess_user_company());

            /* Itens atribuídos - Itens que existem na tabela item, que também estão na cad_item **/
            $atribuido = $this->cockpit_model->getItemsAtribuidos();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            // $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');

            // $totalPendentes = $this->cockpit_model->getItensEmInformacoesPendentes();

            $scadFormatado = $this->cockpit_model->getAtribuicaoCockpitData('sem');

            $totalPendentes = (int) (isset($scadFormatado['pendente_duvidas']) ? $scadFormatado['pendente_duvidas'] : 0);
            $perguntas_respondidas = (int) (isset($scadFormatado['respondido']) ? $scadFormatado['respondido'] : 0);

            $this->cockpit_model->unset_state('filter.evento');
            $this->cockpit_model->unset_state('filter.estabelecimento');
            $this->cockpit_model->unset_state('filter.list_opt');

            $icadFormatado = $this->cockpit_model->getAtribuicaoCockpitData();

            $status = "homologado";

            // $this->applyDefaultFiltersCadItem($status);
            // $aprovados = $this->cad_item_model->get_total_entries($this->homologacoes);
            // $this->cockpit_model->set_state('filter.list_opt', $status);
            $aprovados = (int) (isset($icadFormatado['homologado']) ? $icadFormatado['homologado'] : 0);


            $status = "homologar";
            // $this->applyDefaultFiltersCadItem($status);
            // $homologacao = $this->cad_item_model->get_total_entries($this->homologacoes);
            // $this->cockpit_model->set_state('filter.list_opt', $status);
            $homologacao = (int) (isset($icadFormatado['homologar']) ? $icadFormatado['homologar'] : 0);

            $status = "nao_homologado";
            // $this->applyDefaultFiltersCadItem($status);   
            // $reprovados = $this->cad_item_model->get_total_entries($this->homologacoes);
            // $this->cockpit_model->set_state('filter.list_opt', $status);
            $reprovados = (int) (isset($icadFormatado['nao_homologado']) ? $icadFormatado['nao_homologado'] : 0);

            // $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            // $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            // $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');

            // $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $status = "revisao";
            $this->applyDefaultFiltersCadItem($status);

            $emRevisao = $this->cad_item_model->get_total_entriesV2($this->homologacoes);
            $atribuido = $homologacao + $totalPendentes + $perguntas_respondidas + $aprovados + $reprovados;
            // $atribuido = $aprovados + $totalPendentes + $homologacao + $reprovados;

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'sem_perguntas');

            $naoAtribuido = $this->cockpit_model->getItensEmAnalise();
            // $naoAtribuido = $this->cockpit_model->getItemsRetroativos(true); 

            $this->atribuicao = array(
                array(
                    'descricao'  => 'Atribuído',
                    'quantidade' => $atribuido
                ),
                array(
                    'descricao'  => 'Em Revisão',
                    'quantidade' => $emRevisao
                ),
                array(
                    'descricao'  => 'Não Atribuído',
                    'quantidade' => $naoAtribuido
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->atribuicao
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }
}
